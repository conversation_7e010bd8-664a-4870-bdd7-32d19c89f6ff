#pragma once
#include <fstream>
#include <fcntl.h>
#include <iostream>
#include <unistd.h>
#include <dirent.h>
#include <sys/stat.h>
#include <sys/sysmacros.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <mutex>
#include "../../include/VecTool.h"
#include "kma_driver.h"
using namespace std;
typedef unsigned short UTF16;
#if defined(__aarch64__) && defined(__ANDROID__)
#include "Log.h"
#endif

// KMA驱动全局变量
static Driver *kma_driver = new Driver();
static pid_t g_pid = -1;
static bool kma_initialized = false;
namespace
{
    std::mutex kma_mutex;
}

// KMA驱动辅助函数
inline int getPID(char *PackageName)
{
    if (!kma_driver)
    {
        return -1;
    }

    g_pid = kma_driver->get_pid(PackageName);

    if (g_pid > 0)
    {
        kma_driver->cpuset(0, 4);
        kma_initialized = kma_driver->initpid(g_pid);
    }

    return g_pid;
}

inline long getModuleBase(const char *module_name)
{
    std::lock_guard<std::mutex> lock(kma_mutex);

    if (kma_driver && kma_initialized)
    {
        char *name = const_cast<char *>(module_name);
        return kma_driver->get_module_base(g_pid, name);
    }

    return 0;
}

struct Transform
{
    VecTor3 Scale3D;
    VecTor4 Rotation;
    VecTor3 Translation;
};

struct FMatrix
{
    float M[4][4];
};

class ImGuiTOOL
{
public:
    pid_t Pid = -1;
    float Matrix[4][4] = {0};
    UTF16 BUFFER16[16] = {0};
    Transform MeshTrans, MeshTran;
    uintptr_t ModulesBase[10] = {0};

    TOUCH_INFORMATION touch_information;
    FMatrix XMatrix, BoneMatrix, OutcMatrix;
    uintptr_t MeshAddress = 0, BoneAddress = 0;
    RESOLUTION_INFORMATION resolution_information;
    IMGUISWITCH_INFORMATION imguiswitch_information;

    int readcount(int *c, int num)
    {
        ++*c;
        return num;
    }

private:
    bool ensureInitialized()
    {
        if (!kma_driver)
        {
            return false;
        }

        // 确保 g_pid 和 Pid 同步，如果需要的话进行初始化
        if (!kma_initialized && Pid > 0)
        {
            kma_driver->cpuset(0, 4);
            kma_initialized = kma_driver->initpid(Pid);
            g_pid = Pid;  // 确保全局 g_pid 与实例 Pid 同步
        }

        return kma_initialized;
    }

public:
    bool read(uintptr_t address, void *buffer, size_t size)
    {
        return kma_driver->read(address, buffer, size); // 内核层读取
    }

    template <typename start>
    start read(uintptr_t address)
    {
        start buffer;
        if (read(address, &buffer, sizeof(start)))
        {
            return buffer;
        }
        return {};
    }

    template <typename start>
    bool read(uintptr_t address, start *buffer)
    {
        return read(address, buffer, sizeof(start));
    }

    template <typename... s>
    uintptr_t GetPointer(uintptr_t address, s... args)
    {
        int count = 0;
        uintptr_t last_address = 0;
        int array[] = {(readcount(&count, args))...};
        read(address + array[0], &last_address);
        for (int i = 1; i < count; i++)
        {
            if (i == count - 1)
            {
                last_address += array[i];
                return last_address;
            }
            read(last_address + array[i], &last_address);
        }
        return last_address;
    }

    void GetPid(const char *name)
    {
        Pid = getPID((char *)name);
    }

    uintptr_t GetModuleAddressTwo(char *name)
    {
        return getModuleBase(name);
    }

    ImGuiTOOL()
    {
        memset(&touch_information, 0, sizeof(TOUCH_INFORMATION));
        memset(&resolution_information, 0, sizeof(RESOLUTION_INFORMATION));
        memset(&imguiswitch_information, 0, sizeof(IMGUISWITCH_INFORMATION));
    }
};
