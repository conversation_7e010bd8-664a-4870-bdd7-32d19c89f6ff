#pragma once
#include <fstream>
#include <fcntl.h>
#include <iostream>
#include <unistd.h>
#include <dirent.h>
#include <sys/stat.h>
#include <sys/sysmacros.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <mutex>
#include "../../include/VecTool.h"
#include "kma_driver.h"
using namespace std;
typedef unsigned short UTF16;
#if defined(__aarch64__) && defined(__ANDROID__)
#include <android/log.h>
#define LOG_TAG "MyApp" // 定义日志标签
#define LOGI(...) ((void)__android_log_print(ANDROID_LOG_INFO, LOG_TAG, __VA_ARGS__))
#define LOGE(...) ((void)__android_log_print(ANDROID_LOG_ERROR, LOG_TAG, __VA_ARGS__))
#else
#define LOGI(...) printf(__VA_ARGS__)
#define LOGE(...) printf(__VA_ARGS__)
#endif

// KMA驱动全局变量
static Driver *kma_driver = new Driver();
static bool kma_initialized = false;
namespace
{
    std::mutex kma_mutex;
}

// KMA驱动辅助函数
inline int getPID(char *PackageName)
{
    LOGI("🔧 getPID: 开始获取进程 %s", PackageName);

    if (!kma_driver)
    {
        LOGE("❌ getPID: kma_driver 为空");
        return -1;
    }

    LOGI("🔧 getPID: 调用驱动获取PID...");
    pid_t pid = kma_driver->get_pid(PackageName);
    LOGI("🔧 getPID: 驱动返回PID = %d", pid);

    if (pid > 0)
    {
        LOGI("🔧 getPID: 设置CPU亲和性...");
        kma_driver->cpuset(0, 4);

        LOGI("🔧 getPID: 初始化驱动PID...");
        kma_initialized = kma_driver->initpid(pid);

        if (kma_initialized) {
            LOGI("✅ getPID: 驱动初始化成功");
        } else {
            LOGE("❌ getPID: 驱动初始化失败");
        }
    }
    else
    {
        LOGE("❌ getPID: 获取到无效PID: %d", pid);
    }

    return pid;
}

inline long getModuleBase(const char *module_name, pid_t pid)
{
    LOGI("🔧 getModuleBase: 获取模块 %s 基址, PID=%d", module_name, pid);

    std::lock_guard<std::mutex> lock(kma_mutex);

    if (!kma_driver) {
        LOGE("❌ getModuleBase: kma_driver 为空");
        return 0;
    }

    if (!kma_initialized) {
        LOGE("❌ getModuleBase: kma_driver 未初始化");
        return 0;
    }

    if (pid <= 0) {
        LOGE("❌ getModuleBase: 无效PID: %d", pid);
        return 0;
    }

    LOGI("🔧 getModuleBase: 调用驱动获取模块基址...");
    char *name = const_cast<char *>(module_name);
    long base_addr = kma_driver->get_module_base(pid, name);

    if (base_addr != 0) {
        LOGI("✅ getModuleBase: 成功获取 %s 基址: 0x%lX", module_name, base_addr);
    } else {
        LOGE("❌ getModuleBase: 获取 %s 基址失败", module_name);
    }

    return base_addr;
}

struct Transform
{
    VecTor3 Scale3D;
    VecTor4 Rotation;
    VecTor3 Translation;
};

struct FMatrix
{
    float M[4][4];
};

class ImGuiTOOL
{
public:
    pid_t Pid = -1;
    float Matrix[4][4] = {0};
    UTF16 BUFFER16[16] = {0};
    Transform MeshTrans, MeshTran;
    uintptr_t ModulesBase[10] = {0};

    TOUCH_INFORMATION touch_information;
    FMatrix XMatrix, BoneMatrix, OutcMatrix;
    uintptr_t MeshAddress = 0, BoneAddress = 0;
    RESOLUTION_INFORMATION resolution_information;
    IMGUISWITCH_INFORMATION imguiswitch_information;

    int readcount(int *c, int num)
    {
        ++*c;
        return num;
    }


    bool read(uintptr_t address, void *buffer, size_t size)
    {
        if (!kma_driver)
        {
            LOGE("❌ read: kma_driver 为空");
            return false;
        }

        if (!kma_initialized)
        {
            LOGE("❌ read: kma_driver 未初始化");
            return false;
        }

        bool result = kma_driver->read(address, buffer, size);

        if (!result) {
            LOGE("❌ read: 读取失败 地址=0x%lX 大小=%zu", address, size);
        }

        return result;
    }

    template <typename start>
    start read(uintptr_t address)
    {
        start buffer;
        if (read(address, &buffer, sizeof(start)))
        {
            return buffer;
        }
        return {};
    }

    template <typename start>
    bool read(uintptr_t address, start *buffer)
    {
        return read(address, buffer, sizeof(start));
    }

    template <typename... s>
    uintptr_t GetPointer(uintptr_t address, s... args)
    {
        int count = 0;
        uintptr_t last_address = 0;
        int array[] = {(readcount(&count, args))...};
        read(address + array[0], &last_address);
        for (int i = 1; i < count; i++)
        {
            if (i == count - 1)
            {
                last_address += array[i];
                return last_address;
            }
            read(last_address + array[i], &last_address);
        }
        return last_address;
    }

    void GetPid(const char *name)
    {
        Pid = getPID((char *)name);
    }

    uintptr_t GetModuleAddressTwo(char *name)
    {
        return getModuleBase(name, Pid);
    }

    ImGuiTOOL()
    {
        memset(&touch_information, 0, sizeof(TOUCH_INFORMATION));
        memset(&resolution_information, 0, sizeof(RESOLUTION_INFORMATION));
        memset(&imguiswitch_information, 0, sizeof(IMGUISWITCH_INFORMATION));
    }
};
